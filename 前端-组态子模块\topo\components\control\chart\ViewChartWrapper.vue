<template>
    <div class="view-chart-wrapper" ref="xwin"></div>
</template>

<script>
import BaseView from '../View.vue';
import chartOption from '@/assets/topo-data/chart-option.js';
import { getEchart } from '@/api/scada/echart';

export default {
    name: 'ChartWrapper',
    extends: BaseView,
    data() {
        return {
            echart: null,
            timer: null,
        };
    },
    computed: {
        echartRun() {
            this.$nextTick(function () {
                if (this.detail.dataBind.echartOption && this.detail.dataBind.echartRun > new Date().getTime() - 10000) {
                    let funStr = chartOption.getFun(this.detail.dataBind.echartOption);
                    try {
                        let fun = eval('(' + funStr + ')');
                        let echartData = {};
                        if (this.detail.dataBind.echartData) {
                            try {
                                echartData = JSON.parse(this.detail.dataBind.echartData);
                            } catch (error) {
                                this.$message({
                                    message: '请输入正确的json数据',
                                    type: 'warning',
                                });
                            }
                        }
                        let option = fun(this.$echarts, echartData);
                        this.loadData(option);
                        this.onResize();
                        this.$message({ message: '运行成功', type: 'success' });
                    } catch (error) {
                        console.log(error);
                        this.$message({
                            message: '图表初始化失败，请检查代码视图！',
                            type: 'warning',
                        });
                    }
                }
            });
            return this.detail.dataBind.echartOption + this.detail.dataBind.echartRun;
        },
    },
    watch: {
        echartRun(newColor, oldColor) {
            // console.log('',newColor);
        },
    },
    mounted() {
        if (this.editMode && this.detail.dataBind.echartUrl) {
            let echartSecond = this.detail.dataBind.echartSecond;
            if (!echartSecond) {
                echartSecond = 60 * 1000;
            } else {
                echartSecond = echartSecond * 1000;
            }
            this.getEchartData(this.detail.dataBind.echartUrl);
            this.timer = setInterval(() => {
                this.getEchartData(this.detail.dataBind.echartUrl);
            }, echartSecond);
        } else {
            this.initEchart();
        }
    },
    methods: {
        loadData(option) {
            if (this.echart) {
                this.echart.dispose();
            }
            let view = this.$refs.xwin;
            this.echart = this.$echarts.init(view);
            this.echart.setOption(option);
        },
        onResize(size) {
            if (this.echart) {
                this.echart.resize();
            }
        },
        initEchart() {
            if (!this.detail.dataBind.echartOption) {
                this.detail.dataBind.echartOption = chartOption.getOption();
                let funStr = chartOption.getFun(this.detail.dataBind.echartOption);
                let fun = eval('(' + funStr + ')');
                let echartData = {};
                if (this.detail.dataBind.echartData) {
                    echartData = JSON.parse(this.detail.dataBind.echartData);
                }
                let option = fun(this.$echarts, echartData);
                this.loadData(option);
                this.onResize();
            } else if (this.detail.dataBind.echartOption.indexOf('echartId-') > -1) {
                let id = this.detail.dataBind.echartOption.split('-')[1];
                this.getEchartDataById(id);
            } else {
                let funStr = chartOption.getFun(this.detail.dataBind.echartOption);
                let fun = eval('(' + funStr + ')');
                let echartData = {};
                if (this.detail.dataBind.echartData) {
                    echartData = JSON.parse(this.detail.dataBind.echartData);
                }
                let option = fun(this.$echarts, echartData);
                this.loadData(option);
                this.onResize();
            }
        },
        // 获取自定义echart详情
        getEchartDataById(id) {
            getEchart(id).then((res) => {
                if (res.code === 200) {
                    let funStr = chartOption.getFun(res.data.echartData);
                    let fun = eval('(' + funStr + ')');
                    let echartData = {};
                    if (this.detail.dataBind.echartData) {
                        echartData = JSON.parse(this.detail.dataBind.echartData);
                    }
                    let option = fun(this.echarts, echartData);
                    this.loadData(option);
                    this.onResize();
                }
            });
        },
        getEchartData(dataUrl) {
            this.$axios({
                url: dataUrl,
                method: 'get',
            }).then((res) => {
                this.detail.dataBind.echartData = JSON.stringify(res.data);
                this.initEchart();
            });
        },
    },
    beforeDestroy() {
        clearInterval(this.timer);
        this.timer = null;
    },
};
</script>

<style lang="scss">
.view-chart-wrapper {
    height: 100%;
    width: 100%;
    padding: 10px;
}
</style>
