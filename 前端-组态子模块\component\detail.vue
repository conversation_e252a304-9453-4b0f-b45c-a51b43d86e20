<template>
    <div class="component-detail-wrap">
        <el-row :gutter="12">
            <el-col :span="12">
                <div class="card-wrap">
                    <el-tabs class="tabs-wrap" type="border-card">
                        <el-tab-pane label="Template">
                            <monaco-editor ref="templateEditor" height="75vh" language="html"
                                @change="handleTemplateEditorChange"></monaco-editor>
                        </el-tab-pane>
                        <el-tab-pane label="CSS">
                            <monaco-editor ref="styleEditor" height="75vh" language="css"
                                @change="handleStyleEditorChange"></monaco-editor>
                        </el-tab-pane>
                        <el-tab-pane label="Script">
                            <monaco-editor ref="scriptEditor" language="javascript" height="75vh"
                                @change="handleScriptEditorChange"></monaco-editor>
                        </el-tab-pane>
                    </el-tabs>
                    <div class="tools-wrap">
                        <el-button class="item-btn" style="color: #e6a23c" type="text" @click="handleSubmitForm"
                            v-hasPermi="['scada:component:edit']">
                            <svg-icon icon-class="save" />
                            保存
                        </el-button>
                        <el-button class="item-btn" type="text" icon="el-icon-refresh" @click="handleRun">运行</el-button>
                    </div>
                </div>
            </el-col>
            <el-col :span="12">
                <el-card class="card-wrap">
                    <div slot="header">
                        <span>预览</span>
                    </div>
                    <div ref="componentResult" style="height: 74vh; width: 100%"></div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import Vue from 'vue';
import html2canvas from 'html2canvas';

import { getComponent, updateComponent } from '@/api/scada/component';

export default {
    name: 'ComponentDetail',
    data() {
        return {
            // 表单参数
            form: {
                componentTemplate: '',
                componentStyle: '',
                componentScript: '',
            },
        };
    },
    mounted() {
        this.getDetail();
    },
    methods: {
        getDetail() {
            getComponent(this.$route.query.id).then((res) => {
                if (res.code === 200) {
                    this.form = res.data;
                    this.$refs.templateEditor.setValue(this.form.componentTemplate);
                    this.$refs.styleEditor.setValue(this.form.componentStyle);
                    this.$refs.scriptEditor.setValue(this.form.componentScript);
                    this.loadData();
                }
            });
        },
        loadData() {
            let template = this.form.componentTemplate;
            if (!template) return;
            let styleCss = this.form.componentStyle;
            let style = document.createElement('style');
            style.innerHTML = styleCss;
            document.head.appendChild(style);
            let script = this.form.componentScript;
            if (script) {
                script = script.replace(/export default/, 'return');
            }
            let obj = new Function(script)();
            obj.template = template;
            let Profile = Vue.extend(obj);
            if (this.$refs.componentResult.innerHTML) {
                this.$refs.componentResult.innerHTML = '';
            }
            let newDiv = document.createElement('div');
            newDiv.setAttribute('id', 'component-result');
            this.$refs.componentResult.appendChild(newDiv);
            new Profile().$mount('#component-result');
        },
        //编辑器
        handleTemplateEditorChange(data) {
            this.form.componentTemplate = data;
        },
        handleStyleEditorChange(data) {
            this.form.componentStyle = data;
        },
        handleScriptEditorChange(data) {
            this.form.componentScript = data;
        },
        // 运行
        handleRun() {
            this.loadData();
        },
        // 提交按钮
        handleSubmitForm() {
            let _this = this;
            _this.$modal.loading('保存中，请稍候...');
            let canvasBox = _this.$refs.componentResult;
            html2canvas(canvasBox).then(function (canvas) {
                _this.form.base64 = canvas.toDataURL('image/png');
                updateComponent(_this.form).then((res) => {
                    if (res.code === 200) {
                        _this.$modal.msgSuccess('修改成功');
                    }
                    _this.$modal.closeLoading();
                });
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.component-detail-wrap {
    padding: 20px;

    .card-wrap {
        position: relative;
        height: 86vh;

        .tabs-wrap {
            border-radius: 4px;
            border: 1px solid #e6ebf5;
            background-color: #ffffff;
            overflow: hidden;
            color: #303133;
        }

        .tools-wrap {
            position: absolute;
            right: 0;
            top: 0;
            padding: 0 10px;

            .item-btn {
                padding: 0 5px;
                height: 40px;
            }
        }

        ::v-deep .el-card__header {
            padding: 9px 15px 7px;
            min-height: 39px;
            background-color: #f5f7fa;
            border-bottom: 1px solid #dfe4ed;
        }
    }
}
</style>
