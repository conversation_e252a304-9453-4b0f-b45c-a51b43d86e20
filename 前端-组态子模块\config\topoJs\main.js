import Vue from 'vue';

import Cookies from 'js-cookie';

import Element from 'element-ui';
import './assets/styles/element-variables.scss';
import busEvent from '@/utils/busEvent';
// Echart
import * as echarts from 'echarts';

import '@/assets/styles/index.scss'; // global css
import '@/assets/styles/ruoyi.scss'; // ruoyi css
import App from './App';
import store from './store';
import router from '../router';
import directive from './directive'; // directive
import plugins from './plugins'; // plugins
import { download } from '@/utils/request';

import './permission'; // permission control
import { getDicts } from '@/api/system/dict/data';
import { getConfigKey } from '@/api/system/config';
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from '@/utils/ruoyi';
// 分页组件
import Pagination from '@/components/Pagination';
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar';
// 富文本组件
import Editor from '@/components/Editor';
// 文件上传组件
import FileUpload from '@/components/FileUpload';
// 图片上传组件
import ImageUpload from '@/components/ImageUpload';
// 图片预览组件
import ImagePreview from '@/components/ImagePreview';
// 字典标签组件
import DictTag from '@/components/DictTag';
// 头部标签组件
import VueMeta from 'vue-meta';
// 字典数据组件
import DictData from '@/components/DictData';
//代码编辑器
import MonacoEditor from '@/components/MonacoEditor';
// 一键复制粘贴板组件
import VueClipboard from 'vue-clipboard2';
// Mqtt工具
import mqttTool from '@/utils/mqttTool';

import ItemWrap from './views/bigScreen/components/item-wrap/item-wrap.vue';
import Message from './views/bigScreen/components/message/message.vue';
import Reacquire from './views/bigScreen/components/reacquire/reacquire.vue';
//vue编辑器组件
import Codemirror from 'vue-codemirror';
import 'codemirror/lib/codemirror.css';
import 'codemirror/mode/vue/vue.js';
import topoUtil from '@/utils/topo/topo-util';
//axios
import axios from 'axios';
import VueAxios from 'vue-axios';
import './assets/icons'; // icon
// video player
import VideoPlayer from 'vue-video-player';
import 'vue-video-player/src/custom-theme.css';
import 'video.js/dist/video-js.css';
// DataV
import dataV from '@jiaminghi/data-view';
// 百度地图
import BaiduMap from 'vue-baidu-map';

// 自定义组件
Vue.component('ItemWrap', ItemWrap);
Vue.component('Message', Message);
Vue.component('Reacquire', Reacquire);

// 全局方法挂载
Vue.prototype.getDicts = getDicts;
Vue.prototype.getConfigKey = getConfigKey;
Vue.prototype.parseTime = parseTime;
Vue.prototype.resetForm = resetForm;
Vue.prototype.addDateRange = addDateRange;
Vue.prototype.selectDictLabel = selectDictLabel;
Vue.prototype.selectDictLabels = selectDictLabels;
Vue.prototype.download = download;
Vue.prototype.handleTree = handleTree;
Vue.prototype.$mqttTool = mqttTool;
Vue.prototype.$busEvent = busEvent;
Vue.prototype.$axios = axios;
Vue.prototype.$echarts = echarts;
Vue.prototype.topoUtil = topoUtil;

// 全局组件挂载
Vue.component('DictTag', DictTag);
Vue.component('Pagination', Pagination);
Vue.component('RightToolbar', RightToolbar);
Vue.component('Editor', Editor);
Vue.component('FileUpload', FileUpload);
Vue.component('ImageUpload', ImageUpload);
Vue.component('ImagePreview', ImagePreview);
Vue.component('MonacoEditor', MonacoEditor);
Vue.use(VueClipboard);
Vue.use(directive);
Vue.use(plugins);
Vue.use(VueMeta);
Vue.use(Codemirror);
Vue.use(VueAxios, axios);
Vue.use(VideoPlayer);
Vue.use(dataV);
Vue.use(BaiduMap, { ak: process.env.VUE_APP_BAI_DU_AK });
DictData.install();

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
    size: Cookies.get('size') || 'medium', // set element-ui default size
});

Vue.config.productionTip = false;

new Vue({
    el: '#app',
    router,
    store,
    render: (h) => h(App),
}).$mount('#app');

// 表格带边框
Element.Table.props.border = {
    default: true,
    type: Boolean,
};
