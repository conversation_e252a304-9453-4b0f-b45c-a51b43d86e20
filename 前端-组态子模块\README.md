### 组态子模块介绍

### 组态模块-前端源码

#### 使用方法：

1. 获取源码后，修改文件夹名称为 `scada` ，然后放置于项目的 `wumei-smart/vue/src/views` 文件夹下

```
完整路径如下：
/wumei-smart/vue/src/views/scada
```

2. 将源码中名称为 `config` 文件夹下的 `topo-data` 和 `topo-img` 的文件夹分别剪切到项目`wumei-smart/vue/src/assets` 文件夹下

```
完整路径如下：
/wumei-smart/vue/src/assets/topo-data
/wumei-smart/vue/src/assets/topo-img
```

3. 将源码中名称为 `config` 文件夹下的 `topo` 的文件夹剪切到项目`wumei-smart/vue/src/utils` 文件夹下

```
完整路径如下：
/wumei-smart/vue/src/utils/topo

```

4. 将源码中名称为 `config/topoJs` 文件夹下的 `uid.js` 的文件剪切到项目`wumei-smart/vue/src/utils` 文件夹下

```
完整路径如下：
/wumei-smart/vue/src/utils/uid.js
```

5. 将源码中名称为 `config/topoJs` 文件夹下的 `index.js` 的文件里面的**所有代码**复制到项目`wumei-smart/vue/src/utils/index.js` 文件里面，直接在文件末尾添加即可，对比完成后可删除源码中的这个文件

```
完整路径如下：
/wumei-smart/vue/src/utils/index.js
```

6. 将源码中名称为 `config` 文件夹下的 `scada` 的文件夹剪切到项目`wumei-smart/vue/src/api` 文件夹下

```
完整路径如下：
/wumei-smart/vue/src/api/scada
```

7. 将源码中名称为 `config` 文件夹下的 `json` 和 ` echarts-map-json` 的文件夹剪切到项目`wumei-smart/vue/src/assets` 文件夹下

```
完整路径如下：
/wumei-smart/vue/src/assets/json
/wumei-smart/vue/src/assets/echarts-map-json
```

8. 将源码中名称为 `config` 文件夹下的 `package.json` 和 `vue.config.js`的文件与项目`wumei-smart/vue` 文件夹下的这两个文件,**然后需要分别仔细对比两个文件的差异，将主系统中没有的部分，从组态源码中复制过去**,对比完成后可删除源码中的这两个文件

```
完整路径如下：
/wumei-smart/vue/package.json
/wumei-smart/vue/vue.config.js
```

9. 将源码中名称为 `config` 文件夹下的 `images` 的文件夹下的所有图片复制到项目`wumei-smart/vue/src/assets/images` 文件夹下

```
完整路径如下：
/wumei-smart/vue/src/assets/images`
```

10. 将源码中名称为 `config/topoJs` 文件夹下的 `main.js` 的文件与项目`wumei-smart/vue/src/main.js` 文件仔细对比,将组态源码特有的代码复制到主项目 `wumei-smart/vue/src/main.js`里面，对比完成后可删除源码中的这个文件

```
完整路径如下：
/wumei-smart/vue/src/main.js`
```

11. 将源码中名称为 `config/router` 文件夹下的 `index.txt` 的文件，根据里面的步骤将对应代码复制到项目`wumei-smart/vue/src/router/index.js` 文件里面

```
完整路径如下：
/wumei-smart/vue/src/router/index.js`
```

12. 将源码中名称为 `config` 文件夹下的 `MonacoEditor` 的文件夹剪切到项目`wumei-smart/vue/src/components` 文件夹下

```
完整路径如下：
/wumei-smart/vue/src/components/MonacoEditor`
```

13. 将源码中名称为 `config` 文件夹下的 `svg` 的文件夹里面的**所有图片**复制到项目`wumei-smart/vue/src/assets/icons/svg` 文件夹下

```
完整路径如下：
/wumei-smart/vue/src/assets/icons/svg`
```

14. 将源码中名称为 `config` 文件夹下的 `topo-editor` 的文件夹剪切到项目`wumei-smart/vue/src/store/modules` 文件夹下

```
完整路径如下：
/wumei-smart/vue/src/store/modules/topo-editor`
```

15. 将源码中名称为 `config/topoJs` 文件夹下的 `store-index.js` 的文件，与主系统的 `wumei-smart/vue/src/store/index.js` 文件对比下,将组态源码特有的代码部分复制到主系统此文件中，对比完成后可删除源码中的这个文件

```
完整路径如下：
/wumei-smart/vue/src/store/index.js`
```

16. 命令窗口执行 `npm install` 安装相关插件

17. 命令窗口执行 `npm run dev` 运行项目即可
    <br/><br/>
    <br/><br/>
    <br/><br/>
    <br/><br/>

### 子模块常用命令

```
git clone <repository> --recursive 递归的方式克隆整个项目
git submodule add <repository> <path> 添加子模块
git submodule init 初始化子模块
git submodule update 更新子模块
git submodule foreach git pull 拉取所有子模块
```

### 添加子模块

1. 要向主仓库添加子模块，可以使用以下命令：

```
git submodule add <仓库URL> <路径>
```

例如，我们想将一个名为 submodule_repo 的仓库添加到主仓库的 submodule 目录下，可以运行以下命令：

```
git submodule add https://github.com/user/submodule_repo.git submodule
```

这将会在主仓库中添加一个子模块，并将其克隆到 submodule 目录下。

### 更新子模块

当子模块的远程仓库发生变化时，我们需要手动更新子模块到最新的版本。可以通过以下两个步骤来完成：

```
git submodule update --remote
```

```
git submodule sync
```

第一步使用了 --remote 参数，它会从远程仓库拉取最新的代码。第二步使用了 sync 命令，它会更新子模块的 URL。

### 切换子模块分支

子模块中的分支是独立于主仓库的，因此我们可以在子模块中切换分支，而不会对主仓库产生影响。要在子模块中切换分支，可以使用以下命令：

```
cd submodule
git checkout <分支名>
```

### 子模块的提交历史和分支

子模块有自己独立的提交历史和分支，这使得它能够在主仓库的不同版本间保持一致性。我们可以使用 git log 或 git branch 来查看子模块的提交历史和分支情况。
