1.公共路由里面加入以下代码

 {
     path: '/topo/fullscreen',
     component: () => import('@/views/scada/topo/fullscreen'),
     hidden: true,
 },

2.动态路由里面加入以下代码
 {
        path: '/scada',
        component: Layout,
        hidden: true,
        permissions: ['scada:echart:edit', 'scada:center:edit', 'scada:component:edit'],
        children: [
            {
                path: 'echart/detail',
                component: () => import('@/views/scada/echart/detail'),
                name: 'echartDetail',
                meta: { title: '图表详情', activeMenu: '/scada/echart', noCache: true },
            },
            {
                path: 'topo/editor',
                component: () => import('@/views/scada/topo/editor'),
                name: 'topoEditor',
                meta: { title: '组态详情', activeMenu: '/scada/topo', noCache: true },
            },
            // {
            //     path: 'topo/fullscreen',
            //     component: () => import('@/views/scada/topo/fullscreen'),
            //     name: 'topoFullscreen',
            //     meta: { title: '预览详情', activeMenu: '/scada/topo', noCache: true },
            // },
            {
                path: 'component/detail',
                component: () => import('@/views/scada/component/detail'),
                name: 'Editor',
                meta: { title: '组件详情', activeMenu: '/scada/component', noCache: true },
            },
        ],
    },