<template>
    <div class="gallery-wrap">
        <el-row>
            <el-col :span="4">
                <el-menu class="menu-wrap" :default-active="categoryTypes[0] && categoryTypes[0].dictValue">
                    <el-submenu index="1">
                        <template slot="title">
                            <div class="submenu-title">
                                <i class="el-icon-menu"></i>
                                <span>系统图库</span>
                            </div>
                        </template>
                        <el-menu-item v-for="item in categoryTypes" :key="item.dictValue" :index="item.dictValue"
                            :label="item.dictLabel" :value="item.dictValue" @click="handleTypeClick(item.dictValue)">
                            {{ item.dictLabel }}
                        </el-menu-item>
                    </el-submenu>
                </el-menu>
            </el-col>
            <el-col :span="20">
                <div class="content-wrap">
                    <el-form @submit.native.prevent :model="queryParams" ref="queryForm" :inline="true"
                        v-show="showSearch" label-width="68px">
                        <el-form-item label="文件名称" prop="fileName">
                            <el-input v-model="queryParams.fileName" placeholder="请输入文件名称" clearable size="small"
                                @keyup.enter.native="handleQuery" />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="mini"
                                @click="handleQuery">搜索</el-button>
                            <el-button icon="el-icon-refresh" size="mini" @click="handleResetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>

                    <el-row :gutter="10" class="mb8">
                        <el-col :span="1.5">
                            <el-upload ref="upload" :action="upload.uploadUrl" :headers="upload.headers"
                                :before-upload="beforeUpload" :limit="500" :on-success="handleAvatarSuccess"
                                :show-file-list="false" :file-list="upload.imageList" multiple>
                                <el-button type="primary" plain size="mini" @click="handleUploadFile">
                                    <i class="el-icon-upload el-icon--right" v-hasPermi="['scada:gallery:add']" />
                                    上传
                                </el-button>
                            </el-upload>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
                                @click="handleEdit" v-hasPermi="['scada:gallery:edit']">修改</el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                                @click="handleDelete" v-hasPermi="['scada:gallery:remove']">删除</el-button>
                        </el-col>
                        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
                    </el-row>
                    <div v-loading="loading">
                        <el-checkbox-group class="img-box-wrap" v-if="total !== 0" v-model="checkImages"
                            @change="checkboxChange">
                            <el-card class="img-card" :style="{ margin: '0 10px 10px 0' }" v-for="item in galleryList"
                                :body-style="{ padding: '5px' }" :key="item.id">
                                <img class="img" :src="baseApi + item.resourceUrl" />
                                <div class="name-wrap">
                                    <span>{{ item.fileName }}</span>
                                </div>
                                <el-checkbox class="checkbox" :label="item.id" :key="item.id"><span
                                        v-show="false">占位符</span></el-checkbox>
                            </el-card>
                        </el-checkbox-group>
                        <el-empty description="暂无数据" v-if="total == 0"></el-empty>
                        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                            :limit.sync="queryParams.pageSize" @pagination="getList" :page-sizes="[20, 40, 60]"
                            :page-size="5" />
                    </div>
                </div>
            </el-col>
        </el-row>

        <!-- 修改文件 -->
        <DetailDialog ref="detailDialog" :id="fileId" @save="handleDialogSave"></DetailDialog>
    </div>
</template>

<script>
import DetailDialog from './detail-dialog.vue';
import { getDicts } from '@/api/system/dict/data';
import { listGallery, delGallery } from '@/api/scada/gallery';
import { getToken } from '@/utils/auth';

export default {
    name: 'Gallery',
    components: { DetailDialog },
    data() {
        return {
            baseApi: process.env.VUE_APP_BASE_API,
            loading: true, // 遮罩层
            categoryTypes: [], // 种类类型
            showSearch: true, // 显示搜索条件
            single: true, // 非单个禁用
            multiple: true, // 非多个禁用
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 20,
                categoryName: '',
                fileName: '',
                deptIdStrs: null,
                moduleGuid: '云组态',
                resourceUrl: null,
                orderByColumn: 'id',
                isAsc: 'desc',
            },
            galleryList: [], // 图库管理表格数据
            total: 0, // 总条数
            upload: {
                headers: { Authorization: 'Bearer ' + getToken() }, // 设置上传的请求头部
                uploadUrl: '', // 上传的地址
                imageList: [],
            },
            checkImages: [], // 选中图标
            fileId: null, // 文件id
            ids: [], // 选中数组
        };
    },
    mounted() {
        // 获取数据
        this.getDatas();
    },
    methods: {
        async getDatas() {
            const dictType = 'scada_gallery_type';
            const { data } = (await this.getCategoryTypes(dictType)) || [];
            this.categoryTypes = data;
            this.queryParams.categoryName = data[0].dictValue;
            this.getList();
        },
        // 获取种类
        getCategoryTypes(dictType) {
            return new Promise((resolve, reject) => {
                getDicts(dictType)
                    .then((res) => {
                        resolve(res);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        getList() {
            this.loading = true;
            listGallery(this.queryParams).then((response) => {
                if (response.code === 200) {
                    this.galleryList = response.rows;
                    this.total = response.total;
                    this.checkImages = [];
                }
                this.loading = false;
            });
        },
        // 搜索按钮操作
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        // 重置按钮操作
        handleResetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 种类类型选择
        handleTypeClick(type) {
            this.queryParams.pageNum = 1;
            this.queryParams.categoryName = type;
            this.checkImages = [];
            this.single = true;
            this.multiple = true;
            this.getList();
        },
        // 选中后
        checkboxChange(selection) {
            this.single = selection.length != 1;
            this.multiple = !selection.length;
        },
        // 上传
        handleUploadFile(file) {
            this.upload.uploadUrl = this.baseApi + '/scada/gallery/uploadFile' + '?categoryName=' + this.queryParams.categoryName;
        },
        beforeUpload(file) {
            if (this.queryParams.categoryName == '') {
                this.$message({
                    message: '请选择左侧上传的类型',
                    type: 'warning',
                });
                this.$refs.upload.abort();
                return false;
            }
            const isLt2M = file.size / 1024 / 1024 < 20;
            if (!isLt2M) {
                this.$message.error('上传图片大小不能超过 20MB!');
            }
            return isLt2M;
        },
        handleAvatarSuccess(res, file) {
            if (res.code == 200) {
                this.$message.success('上传成功');
                this.$refs.upload.clearFiles();
                this.getList();
            } else {
                this.$message.error(res.msg);
            }
        },
        // 编辑
        handleEdit(row) {
            this.fileId = row.id || this.checkImages[0];
            this.$refs.detailDialog.open = true;
        },
        handleDialogSave() {
            this.getList();
        },
        // 删除按钮操作
        handleDelete() {
            const ids = this.checkImages;
            this.$confirm('是否确认删除此图标文件么？', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(function () {
                    return delGallery(ids);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                });
        },
    },
};
</script>
<style lang="scss" scoped>
.gallery-wrap {
    .menu-wrap {
        margin-top: 5px;
        height: 946px;
        overflow-x: hidden;
        overflow-y: auto;

        .submenu-title {
            padding: 0 20px;
        }
    }

    .content-wrap {
        padding: 20px;

        .img-box-wrap {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            border-radius: 10px;
            align-content: center;
            background: #fbfaf7;
            padding: 20px 10px 10px 20px;
            margin-top: 10px;

            .img-card {
                width: 165px;
                height: auto;
                text-align: center;
                padding: 10px;
                position: relative;

                .img {
                    width: 100px;
                    height: 100px;
                    margin-top: 10px;
                }

                .name-wrap {
                    text-align: center;
                    font-size: 11px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    margin-top: 10px;
                }

                .checkbox {
                    position: absolute;
                    top: 8px;
                    right: 0px;
                }
            }
        }
    }
}

::-webkit-scrollbar-thumb {
    background-color: #ddd;
}

::-webkit-scrollbar {
    width: 0px;
    height: 0px;
    position: absolute;
}

::-webkit-scrollbar-track {
    background-color: #ddd;
}
</style>
